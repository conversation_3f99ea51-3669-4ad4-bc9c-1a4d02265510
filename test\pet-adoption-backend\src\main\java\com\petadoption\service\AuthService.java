package com.petadoption.service;

import com.petadoption.dto.request.LoginRequest;
import com.petadoption.dto.request.RegisterRequest;
import com.petadoption.dto.response.AuthResponse;
import com.petadoption.dto.response.UserResponse;
import com.petadoption.entity.User;
import com.petadoption.exception.BusinessException;
import com.petadoption.repository.UserRepository;
import com.petadoption.security.UserPrincipal;
import com.petadoption.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 认证服务
 * 处理用户登录、注册、Token验证等认证相关业务
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationManager authenticationManager;
    private final JwtUtil jwtUtil;
    
    /**
     * 用户登录
     * 
     * @param request 登录请求
     * @param clientIp 客户端IP
     * @return 认证响应
     */
    @Transactional
    public AuthResponse login(LoginRequest request, String clientIp) {
        log.info("用户登录尝试: {}", request.getUsername());
        
        try {
            // 执行认证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            request.getUsername(),
                            request.getPassword()
                    )
            );
            
            // 获取认证用户信息
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            // 检查账户状态
            if (!userPrincipal.isEnabled()) {
                throw BusinessException.userNotFound();
            }
            
            if (!userPrincipal.isAccountNonLocked()) {
                throw new BusinessException("账户已被锁定，请联系管理员");
            }
            
            // 生成JWT Token
            String token = jwtUtil.generateToken(
                    userPrincipal.getId(),
                    userPrincipal.getUsername(),
                    userPrincipal.getRole(),
                    userPrincipal.getEmail()
            );
            
            // 更新最后登录信息
            userRepository.updateLastLoginInfo(
                    userPrincipal.getId(),
                    LocalDateTime.now(),
                    clientIp
            );
            
            // 创建用户响应
            UserResponse userResponse = UserResponse.builder()
                    .id(userPrincipal.getId())
                    .username(userPrincipal.getUsername())
                    .email(userPrincipal.getEmail())
                    .phone(userPrincipal.getPhone())
                    .role(userPrincipal.getRole())
                    .enabled(userPrincipal.getEnabled())
                    .build();
            
            log.info("用户登录成功: {} (ID: {})", userPrincipal.getUsername(), userPrincipal.getId());
            
            return AuthResponse.builder()
                    .success(true)
                    .message("登录成功")
                    .token(token)
                    .user(userResponse)
                    .build();
            
        } catch (Exception e) {
            log.warn("用户登录失败: {} - {}", request.getUsername(), e.getMessage());
            throw BusinessException.invalidCredentials();
        }
    }
    
    /**
     * 用户注册
     * 
     * @param request 注册请求
     * @return 认证响应
     */
    @Transactional
    public AuthResponse register(RegisterRequest request) {
        log.info("用户注册尝试: {}", request.getUsername());
        
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new BusinessException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }
        
        // 检查手机号是否已存在（如果提供）
        if (request.getPhone() != null && userRepository.existsByPhone(request.getPhone())) {
            throw new BusinessException("手机号已被注册");
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setRole("user"); // 默认为普通用户
        user.setEnabled(true);
        user.setLocked(false);
        
        // 保存用户
        user = userRepository.save(user);
        
        // 生成JWT Token
        String token = jwtUtil.generateToken(
                user.getId(),
                user.getUsername(),
                user.getRole(),
                user.getEmail()
        );
        
        // 创建用户响应
        UserResponse userResponse = UserResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .phone(user.getPhone())
                .role(user.getRole())
                .enabled(user.getEnabled())
                .build();
        
        log.info("用户注册成功: {} (ID: {})", user.getUsername(), user.getId());
        
        return AuthResponse.builder()
                .success(true)
                .message("注册成功")
                .token(token)
                .user(userResponse)
                .build();
    }
    
    /**
     * 验证Token
     * 
     * @param token JWT Token
     * @return 用户信息
     */
    public UserResponse validateToken(String token) {
        try {
            // 从Token中获取用户名
            String username = jwtUtil.getUsernameFromToken(token);
            
            // 验证Token
            if (!jwtUtil.validateToken(token, username)) {
                throw BusinessException.tokenInvalid();
            }
            
            // 查找用户
            User user = userRepository.findByUsername(username)
                    .orElseThrow(BusinessException::userNotFound);
            
            // 检查用户状态
            if (!user.getEnabled()) {
                throw new BusinessException("账户已被禁用");
            }
            
            if (user.getLocked()) {
                throw new BusinessException("账户已被锁定");
            }
            
            return UserResponse.builder()
                    .id(user.getId())
                    .username(user.getUsername())
                    .email(user.getEmail())
                    .phone(user.getPhone())
                    .role(user.getRole())
                    .enabled(user.getEnabled())
                    .build();
            
        } catch (Exception e) {
            log.warn("Token验证失败: {}", e.getMessage());
            throw BusinessException.tokenInvalid();
        }
    }
    
    /**
     * 获取当前认证用户
     * 
     * @return 当前用户主体
     */
    public UserPrincipal getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            throw BusinessException.unauthorized();
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserPrincipal)) {
            throw BusinessException.unauthorized();
        }
        
        return (UserPrincipal) principal;
    }
    
    /**
     * 获取当前用户ID
     * 
     * @return 当前用户ID
     */
    public Long getCurrentUserId() {
        return getCurrentUser().getId();
    }
    
    /**
     * 检查当前用户是否为管理员
     * 
     * @return 是否为管理员
     */
    public boolean isCurrentUserAdmin() {
        try {
            return getCurrentUser().isAdmin();
        } catch (Exception e) {
            return false;
        }
    }
}
