package com.petadoption.service;

import com.petadoption.dto.request.ApplicationCreateRequest;
import com.petadoption.dto.request.ApplicationReviewRequest;
import com.petadoption.dto.response.ApplicationResponse;
import com.petadoption.entity.Application;
import com.petadoption.entity.Pet;
import com.petadoption.entity.User;
import com.petadoption.exception.BusinessException;
import com.petadoption.repository.ApplicationRepository;
import com.petadoption.repository.PetRepository;
import com.petadoption.repository.UserRepository;
import com.petadoption.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 申请服务
 * 处理领养申请相关的业务逻辑
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApplicationService {
    
    private final ApplicationRepository applicationRepository;
    private final PetRepository petRepository;
    private final UserRepository userRepository;
    private final AuthService authService;
    
    /**
     * 创建领养申请
     * 
     * @param request 申请请求
     * @return 申请响应
     */
    @Transactional
    public ApplicationResponse createApplication(ApplicationCreateRequest request) {
        Long currentUserId = authService.getCurrentUserId();
        log.info("创建领养申请: 用户 {} 申请领养宠物 {}", currentUserId, request.getPetId());
        
        // 检查宠物是否存在且可领养
        Pet pet = petRepository.findById(request.getPetId())
                .orElseThrow(BusinessException::petNotFound);
        
        if (pet.getIsAdopted()) {
            throw BusinessException.petAlreadyAdopted();
        }
        
        // 检查用户是否已申请过该宠物
        if (applicationRepository.existsByUserIdAndPetId(currentUserId, request.getPetId())) {
            throw BusinessException.applicationAlreadyExists();
        }
        
        // 创建申请
        Application application = new Application();
        application.setUserId(currentUserId);
        application.setPetId(request.getPetId());
        application.setReason(request.getReason());
        application.setExperience(request.getExperience());
        application.setLivingSituation(request.getLivingSituation());
        application.setFamilyMembers(request.getFamilyMembers());
        application.setHasOtherPets(request.getHasOtherPets());
        application.setOtherPetsDescription(request.getOtherPetsDescription());
        application.setDailyTime(request.getDailyTime());
        application.setMonthlyBudget(request.getMonthlyBudget());
        application.setNotes(request.getNotes());
        application.setContactPhone(request.getContactPhone());
        application.setContactEmail(request.getContactEmail());
        application.setEmergencyContactName(request.getEmergencyContactName());
        application.setEmergencyContactPhone(request.getEmergencyContactPhone());
        application.setAgreeHomeVisit(request.getAgreeHomeVisit());
        application.setAgreeFollowUp(request.getAgreeFollowUp());
        
        // 保存申请
        application = applicationRepository.save(application);
        
        log.info("领养申请创建成功: ID {}", application.getId());
        
        return convertToResponse(application);
    }
    
    /**
     * 审核申请
     * 
     * @param applicationId 申请ID
     * @param request 审核请求
     * @return 申请响应
     */
    @Transactional
    public ApplicationResponse reviewApplication(Long applicationId, ApplicationReviewRequest request) {
        Long reviewerId = authService.getCurrentUserId();
        log.info("审核申请: ID {}, 审核人: {}, 结果: {}", applicationId, reviewerId, request.getStatus());
        
        // 查找申请
        Application application = applicationRepository.findById(applicationId)
                .orElseThrow(BusinessException::applicationNotFound);
        
        // 检查申请状态
        if (!application.isPending()) {
            throw BusinessException.applicationAlreadyProcessed();
        }
        
        // 执行审核
        if ("approved".equals(request.getStatus())) {
            application.approve(reviewerId, request.getAdminNotes());
            
            // 如果通过，更新宠物状态为已领养
            Pet pet = petRepository.findById(application.getPetId())
                    .orElseThrow(BusinessException::petNotFound);
            pet.setAdopted(application.getUserId());
            petRepository.save(pet);
            
        } else if ("rejected".equals(request.getStatus())) {
            application.reject(reviewerId, request.getAdminNotes());
        } else {
            throw new BusinessException("无效的审核状态: " + request.getStatus());
        }
        
        // 保存申请
        application = applicationRepository.save(application);
        
        log.info("申请审核完成: ID {}, 结果: {}", application.getId(), application.getStatus());
        
        return convertToResponse(application);
    }
    
    /**
     * 取消申请
     * 
     * @param applicationId 申请ID
     */
    @Transactional
    public void cancelApplication(Long applicationId) {
        Long currentUserId = authService.getCurrentUserId();
        log.info("取消申请: ID {}, 用户: {}", applicationId, currentUserId);
        
        // 查找申请
        Application application = applicationRepository.findById(applicationId)
                .orElseThrow(BusinessException::applicationNotFound);
        
        // 检查权限
        if (!application.getUserId().equals(currentUserId) && !authService.isCurrentUserAdmin()) {
            throw BusinessException.applicationPermissionDenied();
        }
        
        // 检查申请状态
        if (!application.isPending()) {
            throw new BusinessException("只能取消待审核的申请");
        }
        
        // 删除申请
        applicationRepository.delete(application);
        
        log.info("申请取消成功: ID {}", applicationId);
    }
    
    /**
     * 获取申请详情
     * 
     * @param applicationId 申请ID
     * @return 申请响应
     */
    @Transactional(readOnly = true)
    public ApplicationResponse getApplicationById(Long applicationId) {
        Application application = applicationRepository.findById(applicationId)
                .orElseThrow(BusinessException::applicationNotFound);
        
        // 检查权限
        Long currentUserId = authService.getCurrentUserId();
        if (!application.getUserId().equals(currentUserId) && !authService.isCurrentUserAdmin()) {
            throw BusinessException.applicationPermissionDenied();
        }
        
        return convertToResponse(application);
    }
    
    /**
     * 获取用户的申请列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 页面大小
     * @param status 状态筛选
     * @return 分页响应
     */
    @Transactional(readOnly = true)
    public PageUtil.PageResponse<ApplicationResponse> getUserApplications(
            Long userId, Integer page, Integer size, String status) {
        
        // 检查权限
        Long currentUserId = authService.getCurrentUserId();
        if (!userId.equals(currentUserId) && !authService.isCurrentUserAdmin()) {
            throw BusinessException.applicationPermissionDenied();
        }
        
        // 创建分页对象
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageUtil.createPageable(page, size, sort);
        
        // 创建查询条件
        Specification<Application> spec = createApplicationSpecification(userId, null, status, null, null);
        
        // 执行查询
        Page<Application> applicationPage = applicationRepository.findAll(spec, pageable);
        
        // 转换响应
        List<ApplicationResponse> applicationResponses = applicationPage.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        // 创建分页响应
        PageUtil.PageResponse<ApplicationResponse> response = new PageUtil.PageResponse<>();
        response.setData(applicationResponses);
        response.setTotal(applicationPage.getTotalElements());
        response.setPage(applicationPage.getNumber() + 1);
        response.setSize(applicationPage.getSize());
        response.setTotalPages(applicationPage.getTotalPages());
        response.setHasNext(applicationPage.hasNext());
        response.setHasPrevious(applicationPage.hasPrevious());
        
        return response;
    }
    
    /**
     * 获取所有申请列表（管理员）
     * 
     * @param page 页码
     * @param size 页面大小
     * @param status 状态筛选
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页响应
     */
    @Transactional(readOnly = true)
    public PageUtil.PageResponse<ApplicationResponse> getAllApplications(
            Integer page, Integer size, String status, LocalDateTime startDate, LocalDateTime endDate) {
        
        // 创建分页对象
        Sort sort = Sort.by(Sort.Direction.ASC, "createTime"); // 待审核的按时间正序
        Pageable pageable = PageUtil.createPageable(page, size, sort);
        
        // 创建查询条件
        Specification<Application> spec = createApplicationSpecification(null, null, status, startDate, endDate);
        
        // 执行查询
        Page<Application> applicationPage = applicationRepository.findAll(spec, pageable);
        
        // 转换响应
        List<ApplicationResponse> applicationResponses = applicationPage.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        // 创建分页响应
        PageUtil.PageResponse<ApplicationResponse> response = new PageUtil.PageResponse<>();
        response.setData(applicationResponses);
        response.setTotal(applicationPage.getTotalElements());
        response.setPage(applicationPage.getNumber() + 1);
        response.setSize(applicationPage.getSize());
        response.setTotalPages(applicationPage.getTotalPages());
        response.setHasNext(applicationPage.hasNext());
        response.setHasPrevious(applicationPage.hasPrevious());
        
        return response;
    }
    
    /**
     * 创建申请查询条件
     */
    private Specification<Application> createApplicationSpecification(
            Long userId, Long petId, String status, LocalDateTime startDate, LocalDateTime endDate) {
        
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 用户ID筛选
            if (userId != null) {
                predicates.add(criteriaBuilder.equal(root.get("userId"), userId));
            }
            
            // 宠物ID筛选
            if (petId != null) {
                predicates.add(criteriaBuilder.equal(root.get("petId"), petId));
            }
            
            // 状态筛选
            if (status != null && !status.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            }
            
            // 时间范围筛选
            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), startDate));
            }
            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), endDate));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
    
    /**
     * 转换为响应对象
     */
    private ApplicationResponse convertToResponse(Application application) {
        ApplicationResponse.ApplicationResponseBuilder builder = ApplicationResponse.builder()
                .id(application.getId())
                .userId(application.getUserId())
                .petId(application.getPetId())
                .status(application.getStatus())
                .reason(application.getReason())
                .experience(application.getExperience())
                .livingSituation(application.getLivingSituation())
                .familyMembers(application.getFamilyMembers())
                .hasOtherPets(application.getHasOtherPets())
                .otherPetsDescription(application.getOtherPetsDescription())
                .dailyTime(application.getDailyTime())
                .monthlyBudget(application.getMonthlyBudget())
                .notes(application.getNotes())
                .adminNotes(application.getAdminNotes())
                .reviewerId(application.getReviewerId())
                .reviewTime(application.getReviewTime())
                .contactPhone(application.getContactPhone())
                .contactEmail(application.getContactEmail())
                .emergencyContactName(application.getEmergencyContactName())
                .emergencyContactPhone(application.getEmergencyContactPhone())
                .agreeHomeVisit(application.getAgreeHomeVisit())
                .agreeFollowUp(application.getAgreeFollowUp())
                .createTime(application.getCreateTime())
                .updateTime(application.getUpdateTime());
        
        // 加载关联的用户信息
        if (application.getUser() != null) {
            User user = application.getUser();
            ApplicationResponse.UserInfo userInfo = ApplicationResponse.UserInfo.builder()
                    .id(user.getId())
                    .username(user.getUsername())
                    .email(user.getEmail())
                    .phone(user.getPhone())
                    .realName(user.getRealName())
                    .build();
            builder.user(userInfo);
        }
        
        // 加载关联的宠物信息
        if (application.getPet() != null) {
            Pet pet = application.getPet();
            ApplicationResponse.PetInfo petInfo = ApplicationResponse.PetInfo.builder()
                    .id(pet.getId())
                    .name(pet.getName())
                    .species(pet.getSpecies())
                    .breed(pet.getBreed())
                    .age(pet.getAge())
                    .gender(pet.getGender())
                    .imageUrl(pet.getImageUrl())
                    .isAdopted(pet.getIsAdopted())
                    .build();
            builder.pet(petInfo);
        }
        
        return builder.build();
    }
}
