<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9daac74e-73d7-4a61-827c-d66ac0cf41bf" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="C:\environment\apache-maven-3.8.8" />
        <option name="localRepository" value="C:\environment\apache-maven-3.8.8\repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="C:\environment\apache-maven-3.8.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="17" />
        <option name="workspaceImportForciblyTurnedOn" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="17" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2xzPxGIqY0KfsdIi0mv1iiawZmy" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.pet-adoption-backend [clean].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.PetAdoptionApplication.executor": "Run",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/IdeaProjects/code/Mongodbdemo",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.dev.executor": "Debug",
    "settings.editor.selected.configurable": "project.propVCSSupport.Mappings",
    "ts.external.directory.path": "C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.1.7\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Spring Boot.PetAdoptionApplication">
    <configuration name="PetAdoptionApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="pet-adoption-backend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.petadoption.PetAdoptionApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/pet-adoption-frontend/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19416.15" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19416.15" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9daac74e-73d7-4a61-827c-d66ac0cf41bf" name="Changes" comment="" />
      <created>1748938173869</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748938173869</updated>
      <workItem from="1748938175114" duration="750000" />
      <workItem from="1748938937953" duration="10855000" />
      <workItem from="1748950568910" duration="1298000" />
      <workItem from="1748951882895" duration="552000" />
      <workItem from="1748952441354" duration="291000" />
      <workItem from="1748956986290" duration="12000" />
      <workItem from="1749020049025" duration="8104000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>