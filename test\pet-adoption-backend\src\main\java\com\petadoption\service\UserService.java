package com.petadoption.service;

import com.petadoption.dto.request.ChangePasswordRequest;
import com.petadoption.dto.request.UpdateProfileRequest;
import com.petadoption.dto.response.UserResponse;
import com.petadoption.entity.User;
import com.petadoption.exception.BusinessException;
import com.petadoption.mapper.UserMapper;
import com.petadoption.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务
 * 处理用户相关的业务逻辑
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {
    
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final AuthService authService;
    
    /**
     * 获取用户详情
     * 
     * @param userId 用户ID
     * @return 用户响应
     */
    @Transactional(readOnly = true)
    public UserResponse getUserById(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw BusinessException.userNotFound();
        }
        
        return convertToResponse(user);
    }
    
    /**
     * 更新用户资料
     * 
     * @param userId 用户ID
     * @param request 更新请求
     * @return 用户响应
     */
    @Transactional
    public UserResponse updateProfile(Long userId, UpdateProfileRequest request) {
        log.info("更新用户资料: ID {}", userId);
        
        // 检查权限
        Long currentUserId = authService.getCurrentUserId();
        if (!userId.equals(currentUserId) && !authService.isCurrentUserAdmin()) {
            throw new BusinessException("无权限修改其他用户的资料");
        }
        
        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw BusinessException.userNotFound();
        }
        
        // 检查邮箱是否被其他用户使用
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            User existingUser = userMapper.findByEmail(request.getEmail());
            if (existingUser != null) {
                throw new BusinessException("邮箱已被其他用户使用");
            }
            user.setEmail(request.getEmail());
        }
        
        // 检查手机号是否被其他用户使用
        if (request.getPhone() != null && !request.getPhone().equals(user.getPhone())) {
            User existingUser = userMapper.findByPhone(request.getPhone());
            if (existingUser != null) {
                throw new BusinessException("手机号已被其他用户使用");
            }
            user.setPhone(request.getPhone());
        }
        
        // 更新其他字段
        if (request.getRealName() != null) {
            user.setRealName(request.getRealName());
        }
        if (request.getIdCard() != null) {
            user.setIdCard(request.getIdCard());
        }
        if (request.getAddress() != null) {
            user.setAddress(request.getAddress());
        }
        if (request.getBio() != null) {
            user.setBio(request.getBio());
        }
        if (request.getAvatarUrl() != null) {
            user.setAvatarUrl(request.getAvatarUrl());
        }
        
        // 保存更新
        userMapper.updateById(user);
        
        log.info("用户资料更新成功: {} (ID: {})", user.getUsername(), user.getId());
        
        return convertToResponse(user);
    }
    
    /**
     * 修改密码
     * 
     * @param userId 用户ID
     * @param request 修改密码请求
     */
    @Transactional
    public void changePassword(Long userId, ChangePasswordRequest request) {
        log.info("修改密码: 用户ID {}", userId);
        
        // 检查权限
        Long currentUserId = authService.getCurrentUserId();
        if (!userId.equals(currentUserId)) {
            throw new BusinessException("只能修改自己的密码");
        }
        
        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw BusinessException.userNotFound();
        }
        
        // 验证旧密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new BusinessException("旧密码不正确");
        }
        
        // 检查新密码是否与旧密码相同
        if (passwordEncoder.matches(request.getNewPassword(), user.getPassword())) {
            throw new BusinessException("新密码不能与旧密码相同");
        }
        
        // 更新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userMapper.updateById(user);
        
        log.info("密码修改成功: 用户 {} (ID: {})", user.getUsername(), user.getId());
    }
    
    /**
     * 更新用户状态（管理员）
     * 
     * @param userId 用户ID
     * @param enabled 启用状态
     */
    @Transactional
    public void updateUserStatus(Long userId, Boolean enabled) {
        log.info("更新用户状态: ID {}, 启用: {}", userId, enabled);
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw BusinessException.userNotFound();
        }
        
        // 不能禁用管理员账户
        if (user.isAdmin() && !enabled) {
            throw new BusinessException("不能禁用管理员账户");
        }
        
        user.setEnabled(enabled);
        userMapper.updateById(user);
        
        log.info("用户状态更新成功: {} (ID: {}), 启用: {}", user.getUsername(), user.getId(), enabled);
    }
    
    /**
     * 锁定/解锁用户（管理员）
     * 
     * @param userId 用户ID
     * @param locked 锁定状态
     */
    @Transactional
    public void updateUserLockStatus(Long userId, Boolean locked) {
        log.info("更新用户锁定状态: ID {}, 锁定: {}", userId, locked);
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw BusinessException.userNotFound();
        }
        
        // 不能锁定管理员账户
        if (user.isAdmin() && locked) {
            throw new BusinessException("不能锁定管理员账户");
        }
        
        user.setLocked(locked);
        userMapper.updateById(user);
        
        log.info("用户锁定状态更新成功: {} (ID: {}), 锁定: {}", user.getUsername(), user.getId(), locked);
    }
    
    /**
     * 删除用户（管理员）
     * 
     * @param userId 用户ID
     */
    @Transactional
    public void deleteUser(Long userId) {
        log.info("删除用户: ID {}", userId);
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw BusinessException.userNotFound();
        }
        
        // 不能删除管理员账户
        if (user.isAdmin()) {
            throw new BusinessException("不能删除管理员账户");
        }
        
        // 删除用户
        userMapper.deleteById(userId);
        
        log.info("用户删除成功: {} (ID: {})", user.getUsername(), user.getId());
    }
    
    /**
     * 转换为响应对象
     */
    private UserResponse convertToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .phone(user.getPhone())
                .role(user.getRole())
                .enabled(user.getEnabled())
                .avatarUrl(user.getAvatarUrl())
                .realName(user.getRealName())
                .address(user.getAddress())
                .bio(user.getBio())
                .lastLoginTime(user.getLastLoginTime())
                .createTime(user.getCreateTime())
                .updateTime(user.getUpdateTime())
                .build();
    }
}
