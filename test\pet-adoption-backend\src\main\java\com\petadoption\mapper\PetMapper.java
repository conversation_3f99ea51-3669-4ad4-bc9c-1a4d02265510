package com.petadoption.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.petadoption.entity.Pet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 宠物Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface PetMapper extends BaseMapper<Pet> {

    /**
     * 根据种类分页查询宠物
     */
    @Select("SELECT * FROM pets WHERE species = #{species} AND is_adopted = false ORDER BY create_time DESC")
    IPage<Pet> findBySpeciesPage(Page<Pet> page, @Param("species") String species);

    /**
     * 根据品种分页查询宠物
     */
    @Select("SELECT * FROM pets WHERE breed = #{breed} AND is_adopted = false ORDER BY create_time DESC")
    IPage<Pet> findByBreedPage(Page<Pet> page, @Param("breed") String breed);

    /**
     * 查询可领养的宠物
     */
    @Select("SELECT * FROM pets WHERE is_adopted = false ORDER BY create_time DESC")
    IPage<Pet> findAvailablePets(Page<Pet> page);

    /**
     * 根据年龄范围查询宠物
     */
    @Select("SELECT * FROM pets WHERE age BETWEEN #{minAge} AND #{maxAge} AND is_adopted = false ORDER BY create_time DESC")
    IPage<Pet> findByAgeRange(Page<Pet> page, @Param("minAge") Integer minAge, @Param("maxAge") Integer maxAge);

    /**
     * 查询最新添加的宠物
     */
    @Select("SELECT * FROM pets WHERE is_adopted = false ORDER BY create_time DESC LIMIT #{limit}")
    List<Pet> findLatestAvailablePets(@Param("limit") Integer limit);

    /**
     * 查询推荐宠物
     */
    @Select("SELECT * FROM pets WHERE species = #{species} AND id != #{excludeId} AND is_adopted = false ORDER BY create_time DESC LIMIT #{limit}")
    List<Pet> findRecommendedPets(@Param("species") String species, @Param("excludeId") Long excludeId, @Param("limit") Integer limit);

    /**
     * 统计待领养宠物数量
     */
    @Select("SELECT COUNT(*) FROM pets WHERE is_adopted = false")
    Long countAvailablePets();

    /**
     * 统计已领养宠物数量
     */
    @Select("SELECT COUNT(*) FROM pets WHERE is_adopted = true")
    Long countAdoptedPets();

    /**
     * 根据种类统计宠物数量
     */
    @Select("SELECT COUNT(*) FROM pets WHERE species = #{species}")
    Long countBySpecies(@Param("species") String species);
}
