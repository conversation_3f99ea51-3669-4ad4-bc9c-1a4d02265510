package com.petadoption.repository;

import com.petadoption.entity.Pet;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 宠物数据访问层
 * 提供宠物相关的数据库操作方法
 * 
 * <AUTHOR> Team
 */
@Repository
public interface PetRepository extends JpaRepository<Pet, Long>, JpaSpecificationExecutor<Pet> {
    
    /**
     * 根据宠物名称查找宠物
     * 
     * @param name 宠物名称
     * @return 宠物列表
     */
    List<Pet> findByName(String name);
    
    /**
     * 根据种类查找宠物
     * 
     * @param species 种类
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findBySpecies(String species, Pageable pageable);
    
    /**
     * 根据品种查找宠物
     * 
     * @param breed 品种
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findByBreed(String breed, Pageable pageable);
    
    /**
     * 根据领养状态查找宠物
     * 
     * @param isAdopted 是否已领养
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findByIsAdopted(Boolean isAdopted, Pageable pageable);
    
    /**
     * 查找待领养的宠物
     * 
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findByIsAdoptedFalse(Pageable pageable);
    
    /**
     * 查找已领养的宠物
     * 
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findByIsAdoptedTrue(Pageable pageable);
    
    /**
     * 根据领养者ID查找宠物
     * 
     * @param adopterId 领养者ID
     * @return 宠物列表
     */
    List<Pet> findByAdopterId(Long adopterId);
    
    /**
     * 根据年龄范围查找宠物
     * 
     * @param minAge 最小年龄
     * @param maxAge 最大年龄
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findByAgeBetween(Integer minAge, Integer maxAge, Pageable pageable);
    
    /**
     * 根据性别查找宠物
     * 
     * @param gender 性别
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findByGender(String gender, Pageable pageable);
    
    /**
     * 模糊查询宠物（名称、品种、描述）
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 宠物列表
     */
    @Query("SELECT p FROM Pet p WHERE " +
           "p.name LIKE %:keyword% OR " +
           "p.breed LIKE %:keyword% OR " +
           "p.description LIKE %:keyword%")
    Page<Pet> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据种类和领养状态查找宠物
     * 
     * @param species 种类
     * @param isAdopted 是否已领养
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findBySpeciesAndIsAdopted(String species, Boolean isAdopted, Pageable pageable);
    
    /**
     * 查找适合有孩子家庭的宠物
     * 
     * @param goodWithKids 是否适合有孩子的家庭
     * @param isAdopted 是否已领养
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findByGoodWithKidsAndIsAdopted(Boolean goodWithKids, Boolean isAdopted, Pageable pageable);
    
    /**
     * 查找适合与其他宠物相处的宠物
     * 
     * @param goodWithPets 是否适合与其他宠物相处
     * @param isAdopted 是否已领养
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findByGoodWithPetsAndIsAdopted(Boolean goodWithPets, Boolean isAdopted, Pageable pageable);
    
    /**
     * 根据活动需求等级查找宠物
     * 
     * @param activityLevel 活动需求等级
     * @param isAdopted 是否已领养
     * @param pageable 分页参数
     * @return 宠物列表
     */
    Page<Pet> findByActivityLevelAndIsAdopted(Integer activityLevel, Boolean isAdopted, Pageable pageable);
    
    /**
     * 查找最新添加的宠物
     * 
     * @param limit 数量限制
     * @return 宠物列表
     */
    @Query("SELECT p FROM Pet p WHERE p.isAdopted = false ORDER BY p.createTime DESC")
    List<Pet> findLatestAvailablePets(Pageable pageable);
    
    /**
     * 查找推荐宠物（同种类、未领养）
     * 
     * @param species 种类
     * @param excludeId 排除的宠物ID
     * @param pageable 分页参数
     * @return 宠物列表
     */
    @Query("SELECT p FROM Pet p WHERE p.species = :species AND p.id != :excludeId AND p.isAdopted = false ORDER BY p.createTime DESC")
    List<Pet> findRecommendedPets(@Param("species") String species, @Param("excludeId") Long excludeId, Pageable pageable);
    
    /**
     * 统计宠物总数
     * 
     * @return 宠物总数
     */
    @Query("SELECT COUNT(p) FROM Pet p")
    long countAllPets();
    
    /**
     * 统计待领养宠物数量
     * 
     * @return 待领养宠物数量
     */
    long countByIsAdoptedFalse();
    
    /**
     * 统计已领养宠物数量
     * 
     * @return 已领养宠物数量
     */
    long countByIsAdoptedTrue();
    
    /**
     * 统计指定种类的宠物数量
     * 
     * @param species 种类
     * @return 宠物数量
     */
    long countBySpecies(String species);
    
    /**
     * 统计指定时间段内添加的宠物数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 宠物数量
     */
    @Query("SELECT COUNT(p) FROM Pet p WHERE p.createTime BETWEEN :startTime AND :endTime")
    long countByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定时间段内领养的宠物数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 领养宠物数量
     */
    @Query("SELECT COUNT(p) FROM Pet p WHERE p.adoptionTime BETWEEN :startTime AND :endTime")
    long countByAdoptionTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                    @Param("endTime") LocalDateTime endTime);
    
    /**
     * 更新宠物领养状态
     * 
     * @param petId 宠物ID
     * @param isAdopted 是否已领养
     * @param adopterId 领养者ID
     * @param adoptionTime 领养时间
     */
    @Modifying
    @Query("UPDATE Pet p SET p.isAdopted = :isAdopted, p.adopterId = :adopterId, p.adoptionTime = :adoptionTime WHERE p.id = :petId")
    void updateAdoptionStatus(@Param("petId") Long petId, 
                             @Param("isAdopted") Boolean isAdopted, 
                             @Param("adopterId") Long adopterId, 
                             @Param("adoptionTime") LocalDateTime adoptionTime);
    
    /**
     * 更新宠物图片URL
     * 
     * @param petId 宠物ID
     * @param imageUrl 图片URL
     */
    @Modifying
    @Query("UPDATE Pet p SET p.imageUrl = :imageUrl WHERE p.id = :petId")
    void updateImageUrl(@Param("petId") Long petId, @Param("imageUrl") String imageUrl);
    
    /**
     * 批量更新宠物领养状态
     * 
     * @param petIds 宠物ID列表
     * @param isAdopted 是否已领养
     */
    @Modifying
    @Query("UPDATE Pet p SET p.isAdopted = :isAdopted WHERE p.id IN :petIds")
    void batchUpdateAdoptionStatus(@Param("petIds") List<Long> petIds, @Param("isAdopted") Boolean isAdopted);
}
