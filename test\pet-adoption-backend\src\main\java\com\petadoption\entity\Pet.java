package com.petadoption.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 宠物实体类
 * 对应数据库中的pets表
 * 与前端宠物数据结构保持一致
 * 
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "pets", indexes = {
    @Index(name = "idx_species", columnList = "species"),
    @Index(name = "idx_status", columnList = "is_adopted"),
    @Index(name = "idx_name", columnList = "name")
})
@TableName("pets")
public class Pet extends BaseEntity {
    
    /**
     * 宠物名称
     */
    @NotBlank(message = "宠物名称不能为空")
    @Size(min = 1, max = 20, message = "宠物名称长度必须在1-20个字符之间")
    @Column(name = "name", nullable = false, length = 20)
    private String name;
    
    /**
     * 宠物种类
     * 狗、猫、兔、鸟、其他
     */
    @NotBlank(message = "宠物种类不能为空")
    @Column(name = "species", nullable = false, length = 10)
    private String species;
    
    /**
     * 宠物品种
     */
    @NotBlank(message = "宠物品种不能为空")
    @Size(max = 30, message = "宠物品种长度不能超过30个字符")
    @Column(name = "breed", nullable = false, length = 30)
    private String breed;
    
    /**
     * 年龄（岁）
     */
    @NotNull(message = "宠物年龄不能为空")
    @Min(value = 0, message = "宠物年龄不能小于0")
    @Max(value = 30, message = "宠物年龄不能大于30")
    @Column(name = "age", nullable = false)
    private Integer age;
    
    /**
     * 性别
     * 公、母、未知
     */
    @NotBlank(message = "宠物性别不能为空")
    @Column(name = "gender", nullable = false, length = 5)
    private String gender;
    
    /**
     * 体重（公斤）
     */
    @DecimalMin(value = "0.1", message = "宠物体重不能小于0.1公斤")
    @DecimalMax(value = "100.0", message = "宠物体重不能大于100公斤")
    @Column(name = "weight", precision = 5, scale = 2)
    private BigDecimal weight;
    
    /**
     * 颜色
     */
    @Size(max = 20, message = "宠物颜色描述长度不能超过20个字符")
    @Column(name = "color", length = 20)
    private String color;
    
    /**
     * 健康状况
     */
    @NotBlank(message = "健康状况不能为空")
    @Size(max = 50, message = "健康状况描述长度不能超过50个字符")
    @Column(name = "health_status", nullable = false, length = 50)
    private String healthStatus;
    
    /**
     * 疫苗接种情况
     */
    @Size(max = 100, message = "疫苗接种情况描述长度不能超过100个字符")
    @Column(name = "vaccination_status", length = 100)
    private String vaccinationStatus;
    
    /**
     * 是否绝育
     */
    @Column(name = "is_neutered", nullable = false)
    private Boolean isNeutered = false;
    
    /**
     * 详细描述
     */
    @NotBlank(message = "宠物描述不能为空")
    @Size(min = 10, max = 1000, message = "宠物描述长度必须在10-1000个字符之间")
    @Column(name = "description", nullable = false, length = 1000)
    private String description;
    
    /**
     * 宠物图片URL
     */
    @Column(name = "image_url", length = 255)
    private String imageUrl;
    
    /**
     * 是否已被领养
     */
    @Column(name = "is_adopted", nullable = false)
    private Boolean isAdopted = false;
    
    /**
     * 领养者ID（如果已被领养）
     */
    @Column(name = "adopter_id")
    private Long adopterId;
    
    /**
     * 领养时间
     */
    @Column(name = "adoption_time")
    private java.time.LocalDateTime adoptionTime;
    
    /**
     * 救助地点
     */
    @Size(max = 100, message = "救助地点描述长度不能超过100个字符")
    @Column(name = "rescue_location", length = 100)
    private String rescueLocation;
    
    /**
     * 救助时间
     */
    @Column(name = "rescue_time")
    private java.time.LocalDateTime rescueTime;
    
    /**
     * 特殊需求
     */
    @Size(max = 200, message = "特殊需求描述长度不能超过200个字符")
    @Column(name = "special_needs", length = 200)
    private String specialNeeds;
    
    /**
     * 性格特点
     */
    @Size(max = 200, message = "性格特点描述长度不能超过200个字符")
    @Column(name = "personality", length = 200)
    private String personality;
    
    /**
     * 是否适合有孩子的家庭
     */
    @Column(name = "good_with_kids")
    private Boolean goodWithKids;
    
    /**
     * 是否适合与其他宠物相处
     */
    @Column(name = "good_with_pets")
    private Boolean goodWithPets;
    
    /**
     * 活动需求等级（1-5）
     */
    @Min(value = 1, message = "活动需求等级不能小于1")
    @Max(value = 5, message = "活动需求等级不能大于5")
    @Column(name = "activity_level")
    private Integer activityLevel;
    
    /**
     * 训练程度（1-5）
     */
    @Min(value = 1, message = "训练程度不能小于1")
    @Max(value = 5, message = "训练程度不能大于5")
    @Column(name = "training_level")
    private Integer trainingLevel;
    
    /**
     * 构造函数
     */
    public Pet() {
        super();
    }
    
    public Pet(String name, String species, String breed, Integer age, String gender) {
        this();
        this.name = name;
        this.species = species;
        this.breed = breed;
        this.age = age;
        this.gender = gender;
    }
    
    /**
     * 是否可以领养
     */
    public boolean isAvailableForAdoption() {
        return !this.isAdopted;
    }
    
    /**
     * 设置为已领养
     */
    public void setAdopted(Long adopterId) {
        this.isAdopted = true;
        this.adopterId = adopterId;
        this.adoptionTime = java.time.LocalDateTime.now();
    }
    
    /**
     * 取消领养
     */
    public void cancelAdoption() {
        this.isAdopted = false;
        this.adopterId = null;
        this.adoptionTime = null;
    }
    
    /**
     * 获取年龄描述
     */
    public String getAgeDescription() {
        if (age == null) return "未知";
        if (age == 0) return "不满1岁";
        if (age == 1) return "1岁";
        return age + "岁";
    }
    
    /**
     * 获取体重描述
     */
    public String getWeightDescription() {
        if (weight == null) return "未知";
        return weight + "公斤";
    }
}
