package com.petadoption.service;

import com.petadoption.dto.request.PetCreateRequest;
import com.petadoption.dto.request.PetUpdateRequest;
import com.petadoption.dto.response.PetResponse;
import com.petadoption.entity.Pet;
import com.petadoption.exception.BusinessException;
import com.petadoption.repository.PetRepository;
import com.petadoption.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 宠物服务
 * 处理宠物相关的业务逻辑
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PetService {
    
    private final PetRepository petRepository;
    
    /**
     * 创建宠物
     * 
     * @param request 创建请求
     * @return 宠物响应
     */
    @Transactional
    public PetResponse createPet(PetCreateRequest request) {
        log.info("创建宠物: {}", request.getName());
        
        // 创建宠物实体
        Pet pet = new Pet();
        pet.setName(request.getName());
        pet.setSpecies(request.getSpecies());
        pet.setBreed(request.getBreed());
        pet.setAge(request.getAge());
        pet.setGender(request.getGender());
        pet.setWeight(request.getWeight());
        pet.setColor(request.getColor());
        pet.setHealthStatus(request.getHealthStatus());
        pet.setVaccinationStatus(request.getVaccinationStatus());
        pet.setIsNeutered(request.getIsNeutered());
        pet.setDescription(request.getDescription());
        pet.setImageUrl(request.getImageUrl());
        pet.setRescueLocation(request.getRescueLocation());
        pet.setRescueTime(request.getRescueTime());
        pet.setSpecialNeeds(request.getSpecialNeeds());
        pet.setPersonality(request.getPersonality());
        pet.setGoodWithKids(request.getGoodWithKids());
        pet.setGoodWithPets(request.getGoodWithPets());
        pet.setActivityLevel(request.getActivityLevel());
        pet.setTrainingLevel(request.getTrainingLevel());
        
        // 保存宠物
        pet = petRepository.save(pet);
        
        log.info("宠物创建成功: {} (ID: {})", pet.getName(), pet.getId());
        
        return convertToResponse(pet);
    }
    
    /**
     * 更新宠物信息
     * 
     * @param petId 宠物ID
     * @param request 更新请求
     * @return 宠物响应
     */
    @Transactional
    public PetResponse updatePet(Long petId, PetUpdateRequest request) {
        log.info("更新宠物信息: ID {}", petId);
        
        // 查找宠物
        Pet pet = petRepository.findById(petId)
                .orElseThrow(BusinessException::petNotFound);
        
        // 更新字段
        if (request.getName() != null) {
            pet.setName(request.getName());
        }
        if (request.getSpecies() != null) {
            pet.setSpecies(request.getSpecies());
        }
        if (request.getBreed() != null) {
            pet.setBreed(request.getBreed());
        }
        if (request.getAge() != null) {
            pet.setAge(request.getAge());
        }
        if (request.getGender() != null) {
            pet.setGender(request.getGender());
        }
        if (request.getWeight() != null) {
            pet.setWeight(request.getWeight());
        }
        if (request.getColor() != null) {
            pet.setColor(request.getColor());
        }
        if (request.getHealthStatus() != null) {
            pet.setHealthStatus(request.getHealthStatus());
        }
        if (request.getVaccinationStatus() != null) {
            pet.setVaccinationStatus(request.getVaccinationStatus());
        }
        if (request.getIsNeutered() != null) {
            pet.setIsNeutered(request.getIsNeutered());
        }
        if (request.getDescription() != null) {
            pet.setDescription(request.getDescription());
        }
        if (request.getImageUrl() != null) {
            pet.setImageUrl(request.getImageUrl());
        }
        if (request.getSpecialNeeds() != null) {
            pet.setSpecialNeeds(request.getSpecialNeeds());
        }
        if (request.getPersonality() != null) {
            pet.setPersonality(request.getPersonality());
        }
        if (request.getGoodWithKids() != null) {
            pet.setGoodWithKids(request.getGoodWithKids());
        }
        if (request.getGoodWithPets() != null) {
            pet.setGoodWithPets(request.getGoodWithPets());
        }
        if (request.getActivityLevel() != null) {
            pet.setActivityLevel(request.getActivityLevel());
        }
        if (request.getTrainingLevel() != null) {
            pet.setTrainingLevel(request.getTrainingLevel());
        }
        
        // 保存更新
        pet = petRepository.save(pet);
        
        log.info("宠物信息更新成功: {} (ID: {})", pet.getName(), pet.getId());
        
        return convertToResponse(pet);
    }
    
    /**
     * 删除宠物
     * 
     * @param petId 宠物ID
     */
    @Transactional
    public void deletePet(Long petId) {
        log.info("删除宠物: ID {}", petId);
        
        // 检查宠物是否存在
        Pet pet = petRepository.findById(petId)
                .orElseThrow(BusinessException::petNotFound);
        
        // 检查是否已被领养
        if (pet.getIsAdopted()) {
            throw new BusinessException("已被领养的宠物不能删除");
        }
        
        // 删除宠物
        petRepository.delete(pet);
        
        log.info("宠物删除成功: {} (ID: {})", pet.getName(), pet.getId());
    }
    
    /**
     * 根据ID获取宠物详情
     * 
     * @param petId 宠物ID
     * @return 宠物响应
     */
    @Transactional(readOnly = true)
    public PetResponse getPetById(Long petId) {
        Pet pet = petRepository.findById(petId)
                .orElseThrow(BusinessException::petNotFound);
        
        return convertToResponse(pet);
    }
    
    /**
     * 分页查询宠物列表
     * 
     * @param page 页码
     * @param size 页面大小
     * @param species 种类筛选
     * @param isAdopted 领养状态筛选
     * @param keyword 关键词搜索
     * @param sortBy 排序字段
     * @param sortDir 排序方向
     * @return 分页响应
     */
    @Transactional(readOnly = true)
    public PageUtil.PageResponse<PetResponse> getPets(
            Integer page, Integer size, String species, Boolean isAdopted, 
            String keyword, String sortBy, String sortDir) {
        
        // 创建分页对象
        Sort sort = PageUtil.createSort(sortDir, sortBy != null ? sortBy : "createTime");
        Pageable pageable = PageUtil.createPageable(page, size, sort);
        
        // 创建查询条件
        Specification<Pet> spec = createPetSpecification(species, isAdopted, keyword);
        
        // 执行查询
        Page<Pet> petPage = petRepository.findAll(spec, pageable);
        
        // 转换响应
        List<PetResponse> petResponses = petPage.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        // 创建分页响应
        PageUtil.PageResponse<PetResponse> response = new PageUtil.PageResponse<>();
        response.setData(petResponses);
        response.setTotal(petPage.getTotalElements());
        response.setPage(petPage.getNumber() + 1);
        response.setSize(petPage.getSize());
        response.setTotalPages(petPage.getTotalPages());
        response.setHasNext(petPage.hasNext());
        response.setHasPrevious(petPage.hasPrevious());
        
        return response;
    }
    
    /**
     * 获取最新宠物列表
     * 
     * @param limit 数量限制
     * @return 宠物列表
     */
    @Transactional(readOnly = true)
    public List<PetResponse> getLatestPets(Integer limit) {
        Pageable pageable = PageUtil.createPageable(1, limit != null ? limit : 6);
        List<Pet> pets = petRepository.findLatestAvailablePets(pageable);
        
        return pets.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取推荐宠物列表
     * 
     * @param petId 当前宠物ID
     * @param limit 数量限制
     * @return 推荐宠物列表
     */
    @Transactional(readOnly = true)
    public List<PetResponse> getRecommendedPets(Long petId, Integer limit) {
        // 获取当前宠物信息
        Pet currentPet = petRepository.findById(petId)
                .orElseThrow(BusinessException::petNotFound);
        
        // 查找同种类的其他宠物
        Pageable pageable = PageUtil.createPageable(1, limit != null ? limit : 4);
        List<Pet> pets = petRepository.findRecommendedPets(
                currentPet.getSpecies(), petId, pageable);
        
        return pets.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    /**
     * 更新宠物领养状态
     * 
     * @param petId 宠物ID
     * @param isAdopted 是否已领养
     * @param adopterId 领养者ID
     */
    @Transactional
    public void updateAdoptionStatus(Long petId, Boolean isAdopted, Long adopterId) {
        log.info("更新宠物领养状态: ID {}, 已领养: {}, 领养者: {}", petId, isAdopted, adopterId);
        
        Pet pet = petRepository.findById(petId)
                .orElseThrow(BusinessException::petNotFound);
        
        if (isAdopted) {
            pet.setAdopted(adopterId);
        } else {
            pet.cancelAdoption();
        }
        
        petRepository.save(pet);
        
        log.info("宠物领养状态更新成功: {} (ID: {})", pet.getName(), pet.getId());
    }
    
    /**
     * 创建宠物查询条件
     */
    private Specification<Pet> createPetSpecification(String species, Boolean isAdopted, String keyword) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 种类筛选
            if (species != null && !species.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("species"), species));
            }
            
            // 领养状态筛选
            if (isAdopted != null) {
                predicates.add(criteriaBuilder.equal(root.get("isAdopted"), isAdopted));
            }
            
            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                String likePattern = "%" + keyword.trim() + "%";
                Predicate namePredicate = criteriaBuilder.like(root.get("name"), likePattern);
                Predicate breedPredicate = criteriaBuilder.like(root.get("breed"), likePattern);
                Predicate descriptionPredicate = criteriaBuilder.like(root.get("description"), likePattern);
                
                predicates.add(criteriaBuilder.or(namePredicate, breedPredicate, descriptionPredicate));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
    
    /**
     * 转换为响应对象
     */
    private PetResponse convertToResponse(Pet pet) {
        return PetResponse.builder()
                .id(pet.getId())
                .name(pet.getName())
                .species(pet.getSpecies())
                .breed(pet.getBreed())
                .age(pet.getAge())
                .gender(pet.getGender())
                .weight(pet.getWeight())
                .color(pet.getColor())
                .healthStatus(pet.getHealthStatus())
                .vaccinationStatus(pet.getVaccinationStatus())
                .isNeutered(pet.getIsNeutered())
                .description(pet.getDescription())
                .imageUrl(pet.getImageUrl())
                .isAdopted(pet.getIsAdopted())
                .adopterId(pet.getAdopterId())
                .adoptionTime(pet.getAdoptionTime())
                .rescueLocation(pet.getRescueLocation())
                .rescueTime(pet.getRescueTime())
                .specialNeeds(pet.getSpecialNeeds())
                .personality(pet.getPersonality())
                .goodWithKids(pet.getGoodWithKids())
                .goodWithPets(pet.getGoodWithPets())
                .activityLevel(pet.getActivityLevel())
                .trainingLevel(pet.getTrainingLevel())
                .createTime(pet.getCreateTime())
                .updateTime(pet.getUpdateTime())
                .build();
    }
}
