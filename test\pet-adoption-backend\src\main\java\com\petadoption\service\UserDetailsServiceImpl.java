package com.petadoption.service;

import com.petadoption.entity.User;

import com.petadoption.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户详情服务实现
 * 为Spring Security提供用户认证信息
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {
    
    private final UserRepository userRepository;
    
    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("加载用户信息: {}", username);
        
        // 根据用户名或邮箱查找用户
        User user = userRepository.findByUsernameOrEmail(username, username)
                .orElseThrow(() -> {
                    log.warn("用户不存在: {}", username);
                    return new UsernameNotFoundException("用户不存在: " + username);
                });
        
        log.debug("成功加载用户信息: {} (ID: {}, Role: {})", user.getUsername(), user.getId(), user.getRole());
        
        return UserPrincipal.create(user);
    }
    
    /**
     * 根据用户ID加载用户详情
     * 
     * @param userId 用户ID
     * @return 用户详情
     */
    @Transactional(readOnly = true)
    public UserDetails loadUserById(Long userId) {
        log.debug("根据ID加载用户信息: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> {
                    log.warn("用户不存在: ID {}", userId);
                    return new UsernameNotFoundException("用户不存在: ID " + userId);
                });
        
        log.debug("成功根据ID加载用户信息: {} (ID: {}, Role: {})", user.getUsername(), user.getId(), user.getRole());
        
        return UserPrincipal.create(user);
    }
}
